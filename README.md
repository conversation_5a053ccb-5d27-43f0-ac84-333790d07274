# AI CLI Agent - Autonomous AI-Powered CLI Tool System

A production-ready, autonomous AI-powered CLI tool system that lives and breathes in your local environment. Built with TypeScript 5.8.3 and Node.js, featuring advanced agent capabilities, comprehensive tool calling, and enterprise-grade architecture.

## 🚀 Features

### 🤖 Autonomous AI Agent System
- **Multi-Provider Support**: OpenAI, Anthropic, Deepseek, Ollama, Gemini, Mistral (all with tool calling)
- **Advanced Tool Calling**: Execute shell commands and file operations autonomously
- **Parallel Tool Execution**: Chain and execute multiple tools simultaneously with dependency analysis
- **Autonomous Task Execution**: Multi-iteration problem solving with learning capabilities
- **Intelligent Context Awareness**: Automatic project discovery and real-time indexing

### 🛠️ Comprehensive Tool System
- **Shell Tool**: Execute any shell command without restrictions (batch, background, monitoring)
- **File Tool**: 20+ file operations (read, write, search, backup, compress, sync, watch)
- **Advanced Features**: Retry mechanisms, parallel execution, dependency analysis
- **Extensible Architecture**: Easy to add custom tools

### 📝 Session & Context Management
- **Persistent Sessions**: Automatic session saving and loading with backup/restore
- **Context Memory**: Full conversation history and project context with snapshots
- **Project Indexing**: Intelligent discovery of 20+ project types and dependencies
- **Real-time Updates**: Dynamic context updates with file watching and change detection
- **Context History**: Snapshot management with diff analysis and rollback capabilities

### 🔧 Enterprise-Grade Features
- **Robust Error Handling**: Graceful degradation and comprehensive error management
- **Advanced Logging**: Structured logging with session tracking
- **Configuration Management**: Flexible YAML-based configuration
- **Security**: Respects file permissions and system security

## 📦 Installation

### Prerequisites
- Node.js 18.0.0 or higher
- npm, yarn, or pnpm
- TypeScript 5.8.3

### Quick Start

1. **Clone and Install**
```bash
git clone <repository-url>
cd ai-cli-agent
npm install
```

2. **Build the Project**
```bash
npm run build
```

3. **Configure API Keys**
```bash
# Copy environment template
cp .env.example .env

# Edit .env with your API keys
# Or use interactive configuration
npm run dev config
```

4. **Start Using**
```bash
# Development mode
npm run dev chat

# Production mode
npm start chat
```

## 🎯 Usage

### Basic Commands

```bash
# Start interactive chat session
ai-cli chat

# Start in specific directory
ai-cli chat --directory /path/to/project

# Use specific provider
ai-cli chat --provider anthropic --model claude-3-5-sonnet-20241022

# Resume existing session
ai-cli chat --session session-id

# Configure settings
ai-cli config

# List all sessions
ai-cli sessions --list

# Check status
ai-cli status

# Execute autonomous tasks
ai-cli task "Analyze this codebase and suggest improvements"

# Project analysis
ai-cli analyze --detailed

# Context management
ai-cli context --refresh --snapshot
```

### Interactive Chat

Once in chat mode, you can:

```
You: Create a new React component for a user profile card

AI: I'll help you create a React component for a user profile card. Let me first examine your project structure to understand the setup and then create the component.

[AI autonomously executes tools to examine project, create files, and provide implementation]
```

### Special Commands in Chat

- `help` - Show available commands
- `clear` - Clear conversation history
- `refresh` - Refresh project context
- `autonomous: <task>` - Execute autonomous multi-step task
- `exit` - Exit chat session

## 🔧 Configuration

### Provider Configuration

The system supports multiple LLM providers:

```yaml
agent:
  provider: openai
  model: gpt-4
  temperature: 0.7
  maxTokens: 4000
  enableToolCalling: true
  enableParallelExecution: true
  maxParallelTools: 3

providers:
  openai:
    apiKey: your_api_key
    baseURL: https://api.openai.com/v1
  
  anthropic:
    apiKey: your_api_key
    baseURL: https://api.anthropic.com
  
  # ... other providers
```

### Context Configuration

```yaml
context:
  indexingEnabled: true
  maxFileSize: 1048576  # 1MB
  excludePatterns:
    - node_modules/**
    - .git/**
    - dist/**
  includePatterns:
    - "**/*.ts"
    - "**/*.js"
    - "**/*.py"
  watchForChanges: true
```

## 🏗️ Architecture

### Core Components

```
src/
├── agents/           # AI agent orchestration
├── tools/            # Tool system (shell, file operations)
├── providers/        # LLM provider implementations
├── session/          # Session and conversation management
├── context/          # Project context and indexing
├── config/           # Configuration management
├── utils/            # Utilities (logging, error handling)
└── types/            # TypeScript type definitions
```

### Key Classes

- **AgentOrchestrator**: Main agent coordination and conversation flow
- **ToolRegistry**: Tool management and execution
- **SessionManager**: Session persistence and conversation history
- **ContextEngine**: Project discovery and context management
- **LLMProviderFactory**: Multi-provider LLM abstraction

## 🛠️ Tool System

### Shell Tool
Execute any shell command with full system access:

```typescript
// Examples of what the AI can do:
await shellTool.execute({
  command: "npm install react",
  cwd: "/project/path"
});

await shellTool.execute({
  command: "git status",
  interactive: true
});
```

### File Tool
Comprehensive file operations:

```typescript
// Read, write, search, manipulate files
await fileTool.execute({
  operation: "read",
  path: "src/components/UserCard.tsx"
});

await fileTool.execute({
  operation: "search",
  path: "src/",
  pattern: "useState",
  options: { regex: true }
});
```

## 🔌 LLM Providers

### Supported Providers

| Provider | Tool Calling | Streaming | Models |
|----------|-------------|-----------|---------|
| OpenAI | ✅ | ✅ | GPT-4, GPT-4 Turbo, GPT-3.5 |
| Anthropic | ✅ | ✅ | Claude 3 Opus, Sonnet, Haiku |
| Deepseek | ✅ | ✅ | Deepseek Chat, Coder |
| Ollama | ✅ | ✅ | Llama, Mistral, CodeLlama |
| Gemini | ✅ | ✅ | Gemini Pro, Pro Vision |
| Mistral | ✅ | ✅ | Mistral Small, Medium, Large |

✅ Full support with enhanced tool calling capabilities

### Provider Setup

#### OpenAI
```bash
export OPENAI_API_KEY="your-api-key"
ai-cli config --provider openai --model gpt-4
```

#### Anthropic
```bash
export ANTHROPIC_API_KEY="your-api-key"
ai-cli config --provider anthropic --model claude-3-5-sonnet-20241022
```

#### Ollama (Local)
```bash
# Install Ollama first: https://ollama.ai
ollama pull llama3
ai-cli config --provider ollama --model llama3
```

## 📊 Session Management

### Session Features
- **Automatic Persistence**: Sessions saved automatically
- **Context Preservation**: Full conversation and project context
- **Session Recovery**: Resume interrupted sessions
- **Export/Import**: Share sessions between environments

### Session Commands
```bash
# List sessions
ai-cli sessions --list

# Delete session
ai-cli sessions --delete session-id

# Export session
ai-cli sessions --export session-id

# Import session
ai-cli sessions --import session-file.json
```

## 🔍 Context Engine

### Project Discovery
The system automatically discovers and indexes:
- Project type (Node.js, Python, Rust, Go, etc.)
- File structure and content
- Dependencies and package information
- Git repository information
- Configuration files

### Real-time Updates
- File system watching for changes
- Automatic context refresh
- Intelligent indexing with configurable patterns

## 🚨 Error Handling

### Robust Error Management
- **Graceful Degradation**: System continues operating despite errors
- **Detailed Logging**: Comprehensive error tracking and debugging
- **User-Friendly Messages**: Clear error explanations
- **Recovery Mechanisms**: Automatic retry and fallback strategies

### Error Types
- `ToolExecutionError`: Tool operation failures
- `LLMProviderError`: LLM API issues
- `SessionError`: Session management problems
- `ConfigurationError`: Configuration issues
- `FileOperationError`: File system errors

## 🧪 Development

### Development Setup
```bash
# Install dependencies
npm install

# Start development mode
npm run dev

# Run tests
npm test

# Lint code
npm run lint

# Build for production
npm run build
```

### Adding Custom Tools
```typescript
import { Tool, ToolResult, AgentContext } from '@/types';

export class CustomTool implements Tool {
  public readonly name = 'custom';
  public readonly description = 'Custom tool description';
  public readonly parameters = {
    type: 'object' as const,
    properties: {
      input: {
        type: 'string',
        description: 'Input parameter'
      }
    },
    required: ['input']
  };

  public async execute(args: any, context: AgentContext): Promise<ToolResult> {
    // Implementation
    return {
      success: true,
      result: 'Custom tool result'
    };
  }
}

// Register the tool
toolRegistry.registerTool(new CustomTool());
```

## 📈 Performance

### Optimization Features
- **Parallel Tool Execution**: Execute multiple tools simultaneously
- **Intelligent Caching**: Context and session caching
- **Efficient Indexing**: Smart file system scanning
- **Memory Management**: Automatic cleanup and garbage collection

### Performance Monitoring
- Tool execution timing
- LLM API usage tracking
- Session statistics
- Error rate monitoring

## 🔒 Security

### Security Features
- **File Permission Respect**: Honors system file permissions
- **API Key Protection**: Secure credential management
- **Sandboxed Execution**: Controlled tool execution environment
- **Audit Logging**: Comprehensive action logging

### Best Practices
- Store API keys in environment variables
- Use least privilege principles
- Regular security updates
- Monitor tool execution logs

## 🤝 Contributing

We welcome contributions! Please see our contributing guidelines for details.

### Development Workflow
1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🆘 Support

- **Documentation**: Check this README and inline code documentation
- **Issues**: Report bugs and request features via GitHub Issues
- **Discussions**: Join community discussions for help and ideas

## 🗺️ Roadmap

### Upcoming Features
- [ ] Plugin system for custom tools
- [ ] Web interface for remote access
- [ ] Team collaboration features
- [ ] Advanced workflow automation
- [ ] Integration with popular development tools
- [ ] Enhanced security features
- [ ] Performance optimizations
- [ ] Additional LLM provider support

---

**Built with ❤️ for developers who want autonomous AI assistance in their local environment.**
