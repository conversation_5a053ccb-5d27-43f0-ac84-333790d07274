{"version": 3, "file": "OpenAIProvider.d.ts", "sourceRoot": "", "sources": ["../../src/providers/OpenAIProvider.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,WAAW,EAAE,UAAU,EAAE,WAAW,EAAE,QAAQ,EAAE,UAAU,EAAE,YAAY,EAAE,MAAM,UAAU,CAAC;AAKpG,qBAAa,cAAe,YAAW,WAAW;IAChD,SAAgB,IAAI,YAAY;IAChC,OAAO,CAAC,MAAM,CAAS;IACvB,OAAO,CAAC,MAAM,CAAS;IACvB,OAAO,CAAC,YAAY,CAAe;gBAEvB,MAAM,EAAE,GAAG;IAeV,gBAAgB,CAAC,MAAM,EAAE,MAAM,EAAE,OAAO,GAAE,UAAe,GAAG,OAAO,CAAC,WAAW,CAAC;IA8E/E,sBAAsB,CAAC,MAAM,EAAE,MAAM,EAAE,OAAO,GAAE,UAAe,GAAG,cAAc,CAAC,MAAM,EAAE,IAAI,EAAE,OAAO,CAAC;IA2D9G,mBAAmB,IAAI,OAAO;IAIxB,QAAQ,CAAC,QAAQ,EAAE,QAAQ,EAAE,OAAO,EAAE,YAAY,GAAG,OAAO,CAAC,UAAU,CAAC;IAYrF,OAAO,CAAC,aAAa;IAsCR,cAAc,IAAI,OAAO,CAAC,OAAO,CAAC;IAYlC,kBAAkB,IAAI,OAAO,CAAC,MAAM,EAAE,CAAC;IAe7C,eAAe,IAAI,MAAM;IAIzB,YAAY,CAAC,KAAK,CAAC,EAAE,MAAM,GAAG,MAAM;IAYpC,cAAc,CAAC,IAAI,EAAE,MAAM,GAAG,MAAM;IAKpC,aAAa,CAAC,KAAK,EAAE;QAAE,YAAY,EAAE,MAAM,CAAC;QAAC,gBAAgB,EAAE,MAAM,CAAA;KAAE,EAAE,KAAK,CAAC,EAAE,MAAM,GAAG,MAAM;CAgBxG"}