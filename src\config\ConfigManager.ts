import fs from 'fs-extra';
import path from 'path';
import yaml from 'yaml';
import { AgentConfig } from '../types';
import { ConfigurationError } from '../utils/ErrorHandler';
import { Logger } from '../utils/Logger';

export interface AppConfig {
  agent: AgentConfig;
  providers: {
    openai?: {
      apiKey?: string;
      baseURL?: string;
      organization?: string;
    };
    anthropic?: {
      apiKey?: string;
      baseURL?: string;
    };
    deepseek?: {
      apiKey?: string;
      baseURL?: string;
    };
    gemini?: {
      apiKey?: string;
    };
    mistral?: {
      apiKey?: string;
      baseURL?: string;
    };
    ollama?: {
      baseURL?: string;
      timeout?: number;
    };
  };
  session: {
    persistenceEnabled: boolean;
    maxSessions: number;
    sessionTimeout: number;
    autoCleanup: boolean;
  };
  context: {
    indexingEnabled: boolean;
    maxFileSize: number;
    excludePatterns: string[];
    includePatterns: string[];
    watchForChanges: boolean;
  };
  logging: {
    level: string;
    enableFileLogging: boolean;
    maxLogFiles: number;
    maxLogSize: string;
  };
}

export class ConfigManager {
  private static instance: ConfigManager;
  private config: AppConfig;
  private configPath: string;
  private logger: Logger;

  private constructor() {
    this.logger = Logger.getInstance();
    this.configPath = path.join(process.cwd(), '.ai-cli', 'config.yaml');
    this.config = this.loadConfig();
  }

  public static getInstance(): ConfigManager {
    if (!ConfigManager.instance) {
      ConfigManager.instance = new ConfigManager();
    }
    return ConfigManager.instance;
  }

  private getDefaultConfig(): AppConfig {
    return {
      agent: {
        provider: 'openai',
        model: 'gpt-4',
        temperature: 0.7,
        maxTokens: 4000,
        enableToolCalling: true,
        enableParallelExecution: true,
        maxParallelTools: 3,
        sessionPersistence: true,
        contextIndexing: true,
        autoDiscovery: true
      },
      providers: {
        openai: {
          baseURL: 'https://api.openai.com/v1'
        },
        anthropic: {
          baseURL: 'https://api.anthropic.com'
        },
        deepseek: {
          baseURL: 'https://api.deepseek.com/v1'
        },
        ollama: {
          baseURL: 'http://localhost:11434',
          timeout: 30000
        }
      },
      session: {
        persistenceEnabled: true,
        maxSessions: 50,
        sessionTimeout: 86400000, // 24 hours
        autoCleanup: true
      },
      context: {
        indexingEnabled: true,
        maxFileSize: 1048576, // 1MB
        excludePatterns: [
          'node_modules/**',
          '.git/**',
          'dist/**',
          'build/**',
          '*.log',
          '.env*',
          '*.tmp',
          '*.temp'
        ],
        includePatterns: [
          '**/*.ts',
          '**/*.js',
          '**/*.py',
          '**/*.java',
          '**/*.cpp',
          '**/*.c',
          '**/*.h',
          '**/*.json',
          '**/*.yaml',
          '**/*.yml',
          '**/*.md',
          '**/*.txt'
        ],
        watchForChanges: true
      },
      logging: {
        level: 'info',
        enableFileLogging: true,
        maxLogFiles: 10,
        maxLogSize: '5MB'
      }
    };
  }

  private loadConfig(): AppConfig {
    try {
      if (fs.existsSync(this.configPath)) {
        const configContent = fs.readFileSync(this.configPath, 'utf-8');
        const userConfig = yaml.parse(configContent);
        return this.mergeConfigs(this.getDefaultConfig(), userConfig);
      } else {
        const defaultConfig = this.getDefaultConfig();
        this.saveConfig(defaultConfig);
        return defaultConfig;
      }
    } catch (error) {
      this.logger.error('Failed to load configuration', { error: (error as Error).message });
      throw new ConfigurationError(`Failed to load configuration: ${(error as Error).message}`);
    }
  }

  private mergeConfigs(defaultConfig: AppConfig, userConfig: any): AppConfig {
    return {
      agent: { ...defaultConfig.agent, ...userConfig.agent },
      providers: {
        openai: { ...defaultConfig.providers.openai, ...userConfig.providers?.openai },
        anthropic: { ...defaultConfig.providers.anthropic, ...userConfig.providers?.anthropic },
        deepseek: { ...defaultConfig.providers.deepseek, ...userConfig.providers?.deepseek },
        gemini: { ...defaultConfig.providers.gemini, ...userConfig.providers?.gemini },
        mistral: { ...defaultConfig.providers.mistral, ...userConfig.providers?.mistral },
        ollama: { ...defaultConfig.providers.ollama, ...userConfig.providers?.ollama }
      },
      session: { ...defaultConfig.session, ...userConfig.session },
      context: { ...defaultConfig.context, ...userConfig.context },
      logging: { ...defaultConfig.logging, ...userConfig.logging }
    };
  }

  private saveConfig(config: AppConfig): void {
    try {
      fs.ensureDirSync(path.dirname(this.configPath));
      const configContent = yaml.stringify(config, { indent: 2 });
      fs.writeFileSync(this.configPath, configContent, 'utf-8');
      this.logger.info('Configuration saved', { path: this.configPath });
    } catch (error) {
      this.logger.error('Failed to save configuration', { error: (error as Error).message });
      throw new ConfigurationError(`Failed to save configuration: ${(error as Error).message}`);
    }
  }

  public getConfig(): AppConfig {
    return this.config;
  }

  public getAgentConfig(): AgentConfig {
    return this.config.agent;
  }

  public getProviderConfig(provider: string): any {
    return this.config.providers[provider as keyof typeof this.config.providers];
  }

  public updateConfig(updates: Partial<AppConfig>): void {
    this.config = this.mergeConfigs(this.config, updates);
    this.saveConfig(this.config);
    this.logger.info('Configuration updated');
  }

  public updateAgentConfig(updates: Partial<AgentConfig>): void {
    this.config.agent = { ...this.config.agent, ...updates };
    this.saveConfig(this.config);
    this.logger.info('Agent configuration updated');
  }

  public setProviderApiKey(provider: string, apiKey: string): void {
    if (!this.config.providers[provider as keyof typeof this.config.providers]) {
      this.config.providers[provider as keyof typeof this.config.providers] = {};
    }
    (this.config.providers[provider as keyof typeof this.config.providers] as any).apiKey = apiKey;
    this.saveConfig(this.config);
    this.logger.info(`API key set for provider: ${provider}`);
  }

  public validateConfig(): boolean {
    try {
      const requiredProviderConfig = this.getProviderConfig(this.config.agent.provider);
      if (!requiredProviderConfig) {
        throw new ConfigurationError(`Provider configuration missing: ${this.config.agent.provider}`);
      }

      if (this.config.agent.provider !== 'ollama' && !requiredProviderConfig.apiKey) {
        throw new ConfigurationError(`API key missing for provider: ${this.config.agent.provider}`);
      }

      return true;
    } catch (error) {
      this.logger.error('Configuration validation failed', { error: (error as Error).message });
      return false;
    }
  }

  public getConfigPath(): string {
    return this.configPath;
  }
}
