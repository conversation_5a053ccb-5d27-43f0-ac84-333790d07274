"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ToolRegistry = void 0;
const ErrorHandler_1 = require("../utils/ErrorHandler");
const Logger_1 = require("../utils/Logger");
const ShellTool_1 = require("./ShellTool");
const FileTool_1 = require("./FileTool");
class ToolRegistry {
    static instance;
    tools;
    logger;
    executionQueue;
    constructor() {
        this.tools = new Map();
        this.logger = Logger_1.Logger.getInstance();
        this.executionQueue = new Map();
        this.registerDefaultTools();
    }
    static getInstance() {
        if (!ToolRegistry.instance) {
            ToolRegistry.instance = new ToolRegistry();
        }
        return ToolRegistry.instance;
    }
    registerDefaultTools() {
        this.registerTool(new ShellTool_1.ShellTool());
        this.registerTool(new FileTool_1.FileTool());
    }
    registerTool(tool) {
        this.tools.set(tool.name, tool);
        this.logger.info(`Tool registered: ${tool.name}`, {
            toolName: tool.name,
            description: tool.description
        });
    }
    unregisterTool(toolName) {
        const removed = this.tools.delete(toolName);
        if (removed) {
            this.logger.info(`Tool unregistered: ${toolName}`);
        }
        return removed;
    }
    getTool(toolName) {
        return this.tools.get(toolName);
    }
    getAllTools() {
        return Array.from(this.tools.values());
    }
    getToolNames() {
        return Array.from(this.tools.keys());
    }
    hasToolCalling() {
        return this.tools.size > 0;
    }
    async executeTool(toolCall, context) {
        const tool = this.getTool(toolCall.name);
        if (!tool) {
            throw new ErrorHandler_1.ToolExecutionError(toolCall.name, `Tool not found: ${toolCall.name}`, { availableTools: this.getToolNames() });
        }
        try {
            this.logger.info(`Executing tool: ${toolCall.name}`, {
                toolName: toolCall.name,
                arguments: toolCall.arguments,
                toolCallId: toolCall.id
            });
            const startTime = Date.now();
            const result = await tool.execute(toolCall.arguments, context);
            const duration = Date.now() - startTime;
            this.logger.info(`Tool execution completed: ${toolCall.name}`, {
                toolName: toolCall.name,
                success: result.success,
                duration,
                toolCallId: toolCall.id
            });
            return {
                ...result,
                metadata: {
                    ...result.metadata,
                    toolCallId: toolCall.id,
                    executionTime: duration
                }
            };
        }
        catch (error) {
            this.logger.error(`Tool execution failed: ${toolCall.name}`, {
                toolName: toolCall.name,
                error: error.message,
                arguments: toolCall.arguments,
                toolCallId: toolCall.id
            });
            if (error instanceof ErrorHandler_1.ToolExecutionError) {
                throw error;
            }
            throw new ErrorHandler_1.ToolExecutionError(toolCall.name, error.message, {
                arguments: toolCall.arguments,
                toolCallId: toolCall.id
            });
        }
    }
    async executeToolsParallel(toolCalls, context, maxParallel = 3) {
        if (toolCalls.length === 0) {
            return [];
        }
        this.logger.info(`Executing ${toolCalls.length} tools in parallel`, {
            toolCalls: toolCalls.map(tc => ({ name: tc.name, id: tc.id })),
            maxParallel
        });
        const results = [];
        const executing = [];
        for (let i = 0; i < toolCalls.length; i++) {
            const toolCall = toolCalls[i];
            // Wait if we've reached the parallel limit
            if (executing.length >= maxParallel) {
                const completed = await Promise.race(executing);
                results[completed.index] = completed.result;
                // Remove completed promise from executing array
                const completedIndex = executing.findIndex(p => p === Promise.resolve(completed));
                if (completedIndex !== -1) {
                    executing.splice(completedIndex, 1);
                }
            }
            // Start new tool execution
            const executionPromise = this.executeTool(toolCall, context)
                .then(result => ({ index: i, result }))
                .catch(error => ({
                index: i,
                result: {
                    success: false,
                    result: null,
                    error: error.message,
                    metadata: { toolCallId: toolCall.id }
                }
            }));
            executing.push(executionPromise);
        }
        // Wait for all remaining executions to complete
        const remainingResults = await Promise.all(executing);
        for (const { index, result } of remainingResults) {
            results[index] = result;
        }
        this.logger.info(`Parallel tool execution completed`, {
            totalTools: toolCalls.length,
            successfulTools: results.filter(r => r.success).length,
            failedTools: results.filter(r => !r.success).length
        });
        return results;
    }
    async executeToolsSequential(toolCalls, context) {
        if (toolCalls.length === 0) {
            return [];
        }
        this.logger.info(`Executing ${toolCalls.length} tools sequentially`, {
            toolCalls: toolCalls.map(tc => ({ name: tc.name, id: tc.id }))
        });
        const results = [];
        for (const toolCall of toolCalls) {
            try {
                const result = await this.executeTool(toolCall, context);
                results.push(result);
                // If a tool fails and it's critical, we might want to stop
                if (!result.success && this.isCriticalTool(toolCall.name)) {
                    this.logger.warn(`Critical tool failed, stopping sequential execution: ${toolCall.name}`);
                    break;
                }
            }
            catch (error) {
                const errorResult = {
                    success: false,
                    result: null,
                    error: error.message,
                    metadata: { toolCallId: toolCall.id }
                };
                results.push(errorResult);
                // Stop on critical tool failure
                if (this.isCriticalTool(toolCall.name)) {
                    this.logger.warn(`Critical tool failed, stopping sequential execution: ${toolCall.name}`);
                    break;
                }
            }
        }
        this.logger.info(`Sequential tool execution completed`, {
            totalTools: toolCalls.length,
            executedTools: results.length,
            successfulTools: results.filter(r => r.success).length,
            failedTools: results.filter(r => !r.success).length
        });
        return results;
    }
    isCriticalTool(toolName) {
        // Define which tools are considered critical
        // If they fail, subsequent tools might not work properly
        const criticalTools = ['shell', 'file'];
        return criticalTools.includes(toolName);
    }
    getToolSchema(toolName) {
        const tool = this.getTool(toolName);
        if (!tool) {
            return null;
        }
        return {
            type: 'function',
            function: {
                name: tool.name,
                description: tool.description,
                parameters: tool.parameters
            }
        };
    }
    getAllToolSchemas() {
        return this.getAllTools().map(tool => this.getToolSchema(tool.name));
    }
    validateToolCall(toolCall) {
        const tool = this.getTool(toolCall.name);
        if (!tool) {
            return false;
        }
        // Basic validation - check if required parameters are present
        const required = tool.parameters.required || [];
        for (const param of required) {
            if (!(param in toolCall.arguments)) {
                this.logger.warn(`Missing required parameter: ${param} for tool: ${toolCall.name}`);
                return false;
            }
        }
        return true;
    }
    getToolUsageStats() {
        // This would be implemented with persistent storage in a real application
        // For now, return empty stats
        const stats = {};
        for (const toolName of this.getToolNames()) {
            stats[toolName] = { calls: 0, successes: 0, failures: 0 };
        }
        return stats;
    }
    async executeToolWithRetry(toolCall, context, maxRetries = 2) {
        let lastError = null;
        for (let attempt = 1; attempt <= maxRetries + 1; attempt++) {
            try {
                const result = await this.executeTool(toolCall, context);
                if (result.success || attempt === maxRetries + 1) {
                    return result;
                }
                // If not successful but not the last attempt, retry
                this.logger.warn(`Tool execution failed, retrying (${attempt}/${maxRetries + 1}): ${toolCall.name}`, {
                    error: result.error,
                    attempt
                });
                // Wait before retry with exponential backoff
                await new Promise(resolve => setTimeout(resolve, Math.pow(2, attempt - 1) * 1000));
            }
            catch (error) {
                lastError = error;
                if (attempt === maxRetries + 1) {
                    throw error;
                }
                this.logger.warn(`Tool execution error, retrying (${attempt}/${maxRetries + 1}): ${toolCall.name}`, {
                    error: error.message,
                    attempt
                });
                // Wait before retry with exponential backoff
                await new Promise(resolve => setTimeout(resolve, Math.pow(2, attempt - 1) * 1000));
            }
        }
        throw lastError || new ErrorHandler_1.ToolExecutionError(toolCall.name, 'Max retries exceeded');
    }
    analyzeDependencies(toolCalls) {
        // Simple dependency analysis - group tools that can run in parallel
        const groups = [];
        const fileOperations = [];
        const shellOperations = [];
        const independentOperations = [];
        for (const toolCall of toolCalls) {
            if (toolCall.name === 'file') {
                // File operations might depend on each other
                const operation = toolCall.arguments.operation;
                if (['read', 'exists', 'stat', 'search', 'glob'].includes(operation)) {
                    // Read operations can run in parallel
                    independentOperations.push(toolCall);
                }
                else {
                    // Write operations should be sequential
                    fileOperations.push(toolCall);
                }
            }
            else if (toolCall.name === 'shell') {
                // Shell operations might have dependencies
                shellOperations.push(toolCall);
            }
            else {
                // Other tools can run independently
                independentOperations.push(toolCall);
            }
        }
        // Add groups in dependency order
        if (independentOperations.length > 0) {
            groups.push(independentOperations);
        }
        if (fileOperations.length > 0) {
            groups.push(fileOperations);
        }
        if (shellOperations.length > 0) {
            groups.push(shellOperations);
        }
        return groups.length > 0 ? groups : [toolCalls];
    }
    createBatches(toolCalls, maxParallel) {
        const batches = [];
        for (let i = 0; i < toolCalls.length; i += maxParallel) {
            batches.push(toolCalls.slice(i, i + maxParallel));
        }
        return batches;
    }
    async executeToolChain(toolCalls, context, options = {}) {
        const { stopOnFailure = false, maxParallel = 3, enableRetry = true } = options;
        this.logger.info(`Executing tool chain with ${toolCalls.length} tools`, {
            stopOnFailure,
            maxParallel,
            enableRetry
        });
        const results = [];
        const dependencyGroups = this.analyzeDependencies(toolCalls);
        for (const group of dependencyGroups) {
            const batches = this.createBatches(group, maxParallel);
            for (const batch of batches) {
                const batchPromises = batch.map(toolCall => enableRetry ?
                    this.executeToolWithRetry(toolCall, context, 2) :
                    this.executeTool(toolCall, context));
                const batchResults = await Promise.allSettled(batchPromises);
                for (let i = 0; i < batchResults.length; i++) {
                    const result = batchResults[i];
                    const toolCall = batch[i];
                    if (result.status === 'fulfilled') {
                        results.push(result.value);
                    }
                    else {
                        const failedResult = {
                            success: false,
                            result: null,
                            error: result.reason?.message || 'Tool execution failed',
                            metadata: {
                                toolName: toolCall.name,
                                toolCallId: toolCall.id
                            }
                        };
                        results.push(failedResult);
                        if (stopOnFailure) {
                            this.logger.error(`Tool chain stopped due to failure: ${toolCall.name}`);
                            return results;
                        }
                    }
                }
            }
        }
        return results;
    }
}
exports.ToolRegistry = ToolRegistry;
//# sourceMappingURL=ToolRegistry.js.map