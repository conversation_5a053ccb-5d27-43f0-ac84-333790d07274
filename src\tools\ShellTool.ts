import { exec, spawn } from 'child_process';
import { promisify } from 'util';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, AgentContext, ShellExecutionOptions, ShellExecutionResult } from '../types';
import { ShellExecutionError } from '../utils/ErrorHandler';
import { Logger } from '../utils/Logger';

const execAsync = promisify(exec);

export class ShellTool implements Tool {
  public readonly name = 'shell';
  public readonly description = 'Execute shell commands with full system access';
  public readonly parameters = {
    type: 'object' as const,
    properties: {
      command: {
        type: 'string',
        description: 'The shell command to execute'
      },
      cwd: {
        type: 'string',
        description: 'Working directory for the command (optional)'
      },
      env: {
        type: 'object',
        description: 'Environment variables (optional)'
      },
      timeout: {
        type: 'number',
        description: 'Timeout in milliseconds (optional, default: 30000)'
      },
      shell: {
        type: 'string',
        description: 'Shell to use (optional, default: system default)'
      },
      interactive: {
        type: 'boolean',
        description: 'Run in interactive mode (optional, default: false)'
      }
    },
    required: ['command']
  };

  private logger: Logger;

  constructor() {
    this.logger = Logger.getInstance();
  }

  public async execute(args: any, context: AgentContext): Promise<ToolResult> {
    const startTime = Date.now();
    
    try {
      const options: ShellExecutionOptions = {
        cwd: args.cwd || context.workingDirectory,
        env: { ...process.env, ...args.env },
        timeout: args.timeout || 30000,
        shell: args.shell,
        encoding: 'utf8',
        maxBuffer: 1024 * 1024 * 10, // 10MB
        killSignal: 'SIGTERM'
      };

      let result: ShellExecutionResult;

      if (args.interactive) {
        result = await this.executeInteractive(args.command, options);
      } else {
        result = await this.executeNonInteractive(args.command, options);
      }

      const duration = Date.now() - startTime;
      result.duration = duration;

      this.logger.logToolExecution(this.name, args, result, duration);

      return {
        success: result.success,
        result: result,
        metadata: {
          command: args.command,
          exitCode: result.exitCode,
          duration: duration,
          cwd: options.cwd
        }
      };

    } catch (error) {
      const duration = Date.now() - startTime;
      this.logger.error(`Shell command execution failed: ${args.command}`, {
        error: (error as Error).message,
        command: args.command,
        duration
      });

      if (error instanceof ShellExecutionError) {
        throw error;
      }

      throw new ShellExecutionError(
        args.command,
        -1,
        (error as Error).message,
        { duration }
      );
    }
  }

  private async executeNonInteractive(
    command: string,
    options: ShellExecutionOptions
  ): Promise<ShellExecutionResult> {
    try {
      const { stdout, stderr } = await execAsync(command, {
        cwd: options.cwd,
        env: options.env,
        timeout: options.timeout,
        shell: options.shell,
        encoding: options.encoding as any,
        maxBuffer: options.maxBuffer,
        killSignal: options.killSignal as any
      });

      return {
        stdout: stdout.toString(),
        stderr: stderr.toString(),
        exitCode: 0,
        command,
        duration: 0, // Will be set by caller
        success: true
      };

    } catch (error: any) {
      const exitCode = error.code || -1;
      const stderr = error.stderr?.toString() || error.message;
      const stdout = error.stdout?.toString() || '';

      if (exitCode !== 0) {
        throw new ShellExecutionError(command, exitCode, stderr);
      }

      return {
        stdout,
        stderr,
        exitCode,
        command,
        duration: 0, // Will be set by caller
        success: false
      };
    }
  }

  private async executeInteractive(
    command: string,
    options: ShellExecutionOptions
  ): Promise<ShellExecutionResult> {
    return new Promise((resolve, reject) => {
      const args = command.split(' ');
      const cmd = args.shift()!;
      
      const child = spawn(cmd, args, {
        cwd: options.cwd,
        env: options.env,
        shell: options.shell,
        stdio: ['pipe', 'pipe', 'pipe']
      });

      let stdout = '';
      let stderr = '';

      child.stdout?.on('data', (data) => {
        const output = data.toString();
        stdout += output;
        console.log(output);
      });

      child.stderr?.on('data', (data) => {
        const output = data.toString();
        stderr += output;
        console.error(output);
      });

      child.on('close', (code) => {
        const result: ShellExecutionResult = {
          stdout,
          stderr,
          exitCode: code || 0,
          command,
          duration: 0, // Will be set by caller
          success: (code || 0) === 0
        };

        if (result.success) {
          resolve(result);
        } else {
          reject(new ShellExecutionError(command, result.exitCode, stderr));
        }
      });

      child.on('error', (error) => {
        reject(new ShellExecutionError(command, -1, error.message));
      });

      // Set timeout
      if (options.timeout) {
        setTimeout(() => {
          child.kill(options.killSignal as any);
          reject(new ShellExecutionError(command, -1, 'Command timed out'));
        }, options.timeout);
      }
    });
  }

  // Utility methods for common shell operations
  public async listDirectory(path: string, context: AgentContext): Promise<ToolResult> {
    const isWindows = process.platform === 'win32';
    const command = isWindows ? `dir "${path}"` : `ls -la "${path}"`;
    return this.execute({ command }, context);
  }

  public async getCurrentDirectory(context: AgentContext): Promise<ToolResult> {
    const isWindows = process.platform === 'win32';
    const command = isWindows ? 'cd' : 'pwd';
    return this.execute({ command }, context);
  }

  public async checkCommandExists(commandName: string, context: AgentContext): Promise<boolean> {
    try {
      const isWindows = process.platform === 'win32';
      const command = isWindows ? `where ${commandName}` : `which ${commandName}`;
      const result = await this.execute({ command }, context);
      return result.success;
    } catch {
      return false;
    }
  }

  public async getSystemInfo(context: AgentContext): Promise<ToolResult> {
    const isWindows = process.platform === 'win32';
    const command = isWindows ? 'systeminfo' : 'uname -a';
    return this.execute({ command }, context);
  }

  public async executeWithPipe(commands: string[], context: AgentContext): Promise<ToolResult> {
    const isWindows = process.platform === 'win32';
    const pipeOperator = isWindows ? '|' : '|';
    const combinedCommand = commands.join(` ${pipeOperator} `);

    return this.execute({ command: combinedCommand }, context);
  }

  public async executeInBackground(command: string, context: AgentContext): Promise<ToolResult> {
    const isWindows = process.platform === 'win32';
    const backgroundCommand = isWindows ? `start /B ${command}` : `${command} &`;

    return this.execute({ command: backgroundCommand }, context);
  }

  public async listProcesses(context: AgentContext): Promise<ToolResult> {
    const isWindows = process.platform === 'win32';
    const command = isWindows ? 'tasklist' : 'ps aux';

    return this.execute({ command }, context);
  }

  public async killProcess(pid: number, context: AgentContext): Promise<ToolResult> {
    const isWindows = process.platform === 'win32';
    const command = isWindows ? `taskkill /PID ${pid} /F` : `kill -9 ${pid}`;

    return this.execute({ command }, context);
  }

  public async findProcess(processName: string, context: AgentContext): Promise<ToolResult> {
    const isWindows = process.platform === 'win32';
    const command = isWindows ?
      `tasklist /FI "IMAGENAME eq ${processName}"` :
      `ps aux | grep ${processName}`;

    return this.execute({ command }, context);
  }

  public async getEnvironmentVariables(context: AgentContext): Promise<ToolResult> {
    const isWindows = process.platform === 'win32';
    const command = isWindows ? 'set' : 'env';

    return this.execute({ command }, context);
  }

  public async getDiskUsage(path: string, context: AgentContext): Promise<ToolResult> {
    const isWindows = process.platform === 'win32';
    const command = isWindows ? `dir "${path}" /-c` : `du -sh "${path}"`;

    return this.execute({ command }, context);
  }

  public async getNetworkInfo(context: AgentContext): Promise<ToolResult> {
    const isWindows = process.platform === 'win32';
    const command = isWindows ? 'ipconfig /all' : 'ifconfig -a';

    return this.execute({ command }, context);
  }

  public async monitorCommand(command: string, context: AgentContext, intervalMs: number = 5000): Promise<ToolResult> {
    // This is a simplified monitoring implementation
    // In a real scenario, you'd set up proper monitoring with intervals
    const monitoringCommand = `echo "Monitoring: ${command}" && ${command}`;

    return this.execute({
      command: monitoringCommand,
      timeout: intervalMs * 2
    }, context);
  }

  public async executeWithRetry(
    command: string,
    context: AgentContext,
    maxRetries: number = 3,
    retryDelay: number = 1000
  ): Promise<ToolResult> {
    let lastError: Error | null = null;

    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        const result = await this.execute({ command }, context);

        if (result.success) {
          return result;
        }

        if (attempt === maxRetries) {
          return result;
        }

        this.logger.warn(`Shell command failed, retrying (${attempt}/${maxRetries}): ${command}`, {
          error: result.result?.stderr || 'Unknown error',
          attempt
        });

        // Wait before retry
        await new Promise(resolve => setTimeout(resolve, retryDelay * attempt));

      } catch (error) {
        lastError = error as Error;

        if (attempt === maxRetries) {
          throw error;
        }

        this.logger.warn(`Shell command error, retrying (${attempt}/${maxRetries}): ${command}`, {
          error: (error as Error).message,
          attempt
        });

        // Wait before retry
        await new Promise(resolve => setTimeout(resolve, retryDelay * attempt));
      }
    }

    throw lastError || new ShellExecutionError(command, -1, 'Max retries exceeded');
  }

  public async executeBatch(commands: string[], context: AgentContext, stopOnFailure: boolean = false): Promise<ToolResult[]> {
    const results: ToolResult[] = [];

    for (const command of commands) {
      try {
        const result = await this.execute({ command }, context);
        results.push(result);

        if (!result.success && stopOnFailure) {
          this.logger.warn(`Batch execution stopped due to failure: ${command}`);
          break;
        }
      } catch (error) {
        const failedResult: ToolResult = {
          success: false,
          result: null,
          error: (error as Error).message,
          metadata: { command }
        };
        results.push(failedResult);

        if (stopOnFailure) {
          this.logger.warn(`Batch execution stopped due to error: ${command}`);
          break;
        }
      }
    }

    return results;
  }
}
