import { LLMProvider } from '../types';
export declare class LLMProviderFactory {
    private static instance;
    private providers;
    private logger;
    private configManager;
    private constructor();
    static getInstance(): LLMProviderFactory;
    createProvider(providerName: string): Promise<LLMProvider>;
    getProvider(providerName?: string): Promise<LLMProvider>;
    getSupportedProviders(): string[];
    isProviderSupported(providerName: string): boolean;
    testProvider(provider: LLMProvider): Promise<boolean>;
    testAllProviders(): Promise<Record<string, boolean>>;
    clearProviderCache(providerName?: string): void;
    getProviderCapabilities(providerName: string): {
        supportsToolCalling: boolean;
        supportsStreaming: boolean;
        maxTokens: number;
        supportedModels: string[];
    };
    switchProvider(newProviderName: string): Promise<LLMProvider>;
    getProviderInfo(providerName: string): {
        name: string;
        description: string;
        website: string;
        pricing: string;
        features: string[];
    };
}
//# sourceMappingURL=LLMProviderFactory.d.ts.map