import fs from 'fs-extra';
import path from 'path';
import glob from 'fast-glob';
import simpleGit from 'simple-git';
import toml from 'toml';
import semver from 'semver';
import mime from 'mime-types';
import { ProjectContext, FileInfo, GitInfo, PackageInfo } from '../types';
import { Logger } from '../utils/Logger';
import { ConfigManager } from '../config/ConfigManager';

export class ProjectIndexer {
  private static instance: ProjectIndexer;
  private logger: Logger;
  private configManager: ConfigManager;

  private constructor() {
    this.logger = Logger.getInstance();
    this.configManager = ConfigManager.getInstance();
  }

  public static getInstance(): ProjectIndexer {
    if (!ProjectIndexer.instance) {
      ProjectIndexer.instance = new ProjectIndexer();
    }
    return ProjectIndexer.instance;
  }

  public async indexProject(rootPath: string): Promise<ProjectContext> {
    const startTime = Date.now();
    
    try {
      this.logger.info(`Starting project indexing: ${rootPath}`);

      const [projectType, files, gitInfo, packageInfo] = await Promise.all([
        this.detectProjectType(rootPath),
        this.indexFiles(rootPath),
        this.getGitInfo(rootPath),
        this.getPackageInfo(rootPath)
      ]);

      const dependencies = this.extractDependencies(packageInfo);

      const context: ProjectContext = {
        rootPath,
        projectType,
        dependencies,
        files,
        gitInfo,
        packageInfo
      };

      const duration = Date.now() - startTime;
      this.logger.info(`Project indexing completed`, {
        rootPath,
        projectType,
        fileCount: files.length,
        dependencyCount: Object.keys(dependencies).length,
        duration
      });

      return context;

    } catch (error) {
      this.logger.error('Project indexing failed', {
        rootPath,
        error: (error as Error).message
      });
      throw error;
    }
  }

  private async detectProjectType(rootPath: string): Promise<string> {
    const indicators = [
      { file: 'package.json', type: 'nodejs' },
      { file: 'requirements.txt', type: 'python' },
      { file: 'pyproject.toml', type: 'python' },
      { file: 'setup.py', type: 'python' },
      { file: 'Pipfile', type: 'python' },
      { file: 'Cargo.toml', type: 'rust' },
      { file: 'go.mod', type: 'go' },
      { file: 'pom.xml', type: 'java' },
      { file: 'build.gradle', type: 'java' },
      { file: 'build.gradle.kts', type: 'java' },
      { file: 'composer.json', type: 'php' },
      { file: 'Gemfile', type: 'ruby' },
      { file: 'CMakeLists.txt', type: 'cpp' },
      { file: 'Makefile', type: 'cpp' },
      { file: 'pubspec.yaml', type: 'dart' },
      { file: 'mix.exs', type: 'elixir' },
      { file: 'deno.json', type: 'deno' },
      { file: 'bun.lockb', type: 'bun' },
      { file: 'Podfile', type: 'ios' },
      { file: 'AndroidManifest.xml', type: 'android' },
      { file: 'flutter.yaml', type: 'flutter' }
    ];

    // Check for exact file matches first
    for (const indicator of indicators) {
      const filePath = path.join(rootPath, indicator.file);
      if (await fs.pathExists(filePath)) {
        return indicator.type;
      }
    }

    // Check for pattern-based files
    try {
      const files = await fs.readdir(rootPath);

      // Check for .csproj, .sln files
      if (files.some(file => file.endsWith('.csproj') || file.endsWith('.sln'))) {
        return 'csharp';
      }

      // Check for .xcodeproj directories
      if (files.some(file => file.endsWith('.xcodeproj'))) {
        return 'ios';
      }

      // Count file extensions to determine primary language
      const extCounts: Record<string, number> = {};

      for (const file of files) {
        const ext = path.extname(file).toLowerCase();
        if (ext) {
          extCounts[ext] = (extCounts[ext] || 0) + 1;
        }
      }

      // Sort by count and determine type
      const sortedExts = Object.entries(extCounts).sort(([,a], [,b]) => b - a);

      if (sortedExts.length > 0) {
        const [mostCommonExt, count] = sortedExts[0];

        // Only consider if there are enough files of this type
        if (count >= 2) {
          switch (mostCommonExt) {
            case '.py':
              return 'python';
            case '.js':
            case '.ts':
            case '.jsx':
            case '.tsx':
              return 'nodejs';
            case '.rs':
              return 'rust';
            case '.go':
              return 'go';
            case '.java':
            case '.kt':
              return 'java';
            case '.php':
              return 'php';
            case '.rb':
              return 'ruby';
            case '.cs':
              return 'csharp';
            case '.cpp':
            case '.cc':
            case '.cxx':
            case '.c':
            case '.h':
            case '.hpp':
              return 'cpp';
            case '.swift':
              return 'swift';
            case '.dart':
              return 'dart';
            case '.ex':
            case '.exs':
              return 'elixir';
            case '.html':
            case '.css':
              return 'web';
            case '.vue':
              return 'vue';
            case '.svelte':
              return 'svelte';
            default:
              break;
          }
        }
      }
    } catch (error) {
      this.logger.debug('Failed to analyze file extensions', {
        rootPath,
        error: (error as Error).message
      });
    }

    return 'unknown';
  }

  private async indexFiles(rootPath: string): Promise<FileInfo[]> {
    const config = this.configManager.getConfig().context;
    const files: FileInfo[] = [];

    try {
      const globPatterns = config.includePatterns.length > 0 
        ? config.includePatterns 
        : ['**/*'];

      const filePaths = await glob(globPatterns, {
        cwd: rootPath,
        ignore: config.excludePatterns,
        onlyFiles: false,
        absolute: true,
        stats: true
      });

      for (const entry of filePaths) {
        try {
          const filePath = typeof entry === 'string' ? entry : entry.path;
          const stats = typeof entry === 'string' 
            ? await fs.stat(filePath) 
            : entry.stats!;

          // Skip files that are too large
          if (stats.isFile() && stats.size > config.maxFileSize) {
            continue;
          }

          const fileInfo: FileInfo = {
            path: path.relative(rootPath, filePath),
            type: stats.isDirectory() ? 'directory' : 'file',
            size: stats.size,
            lastModified: stats.mtime,
            permissions: stats.mode.toString(8)
          };

          // Read content for small text files
          if (stats.isFile() && 
              stats.size <= config.maxFileSize && 
              this.isTextFile(filePath)) {
            try {
              fileInfo.content = await fs.readFile(filePath, 'utf8');
            } catch (error) {
              // Skip files that can't be read as text
            }
          }

          files.push(fileInfo);

        } catch (error) {
          this.logger.debug('Failed to index file', {
            file: typeof entry === 'string' ? entry : entry.path,
            error: (error as Error).message
          });
        }
      }

    } catch (error) {
      this.logger.error('Failed to index files', {
        rootPath,
        error: (error as Error).message
      });
    }

    return files;
  }

  private isTextFile(filePath: string): boolean {
    const ext = path.extname(filePath).toLowerCase();

    // Known text extensions
    const textExtensions = [
      '.txt', '.md', '.json', '.yaml', '.yml', '.xml', '.html', '.css',
      '.js', '.ts', '.jsx', '.tsx', '.py', '.java', '.c', '.cpp', '.h',
      '.rs', '.go', '.php', '.rb', '.cs', '.sh', '.bat', '.ps1',
      '.sql', '.r', '.scala', '.kt', '.swift', '.dart', '.lua',
      '.vim', '.ini', '.cfg', '.conf', '.log', '.csv', '.toml',
      '.dockerfile', '.gitignore', '.gitattributes', '.editorconfig',
      '.env', '.example', '.sample', '.template'
    ];

    if (textExtensions.includes(ext)) {
      return true;
    }

    // Use mime-types to check if it's a text file
    const mimeType = mime.lookup(filePath);
    if (mimeType && mimeType.startsWith('text/')) {
      return true;
    }

    // Check for files without extensions that are typically text
    const basename = path.basename(filePath).toLowerCase();
    const textFiles = [
      'readme', 'license', 'changelog', 'makefile', 'dockerfile',
      'jenkinsfile', 'vagrantfile', 'gemfile', 'rakefile'
    ];

    return textFiles.includes(basename);
  }

  private async getGitInfo(rootPath: string): Promise<GitInfo | undefined> {
    try {
      const gitDir = path.join(rootPath, '.git');
      if (!await fs.pathExists(gitDir)) {
        return undefined;
      }

      const git = simpleGit(rootPath);

      // Get current branch
      const branch = await git.revparse(['--abbrev-ref', 'HEAD']);

      // Get remotes
      const remotes = await git.getRemotes(true);
      const remoteUrls = remotes.map(remote => remote.refs.fetch || remote.refs.push);

      // Get status
      const status = await git.status();
      const statusText = status.files.length > 0 ? 'dirty' : 'clean';

      // Get last commit
      const log = await git.log({ maxCount: 1 });
      const lastCommit = log.latest ?
        `${log.latest.hash.substring(0, 7)} - ${log.latest.message}` : '';

      const gitInfo: GitInfo = {
        branch: branch.trim(),
        remotes: remoteUrls,
        status: statusText,
        lastCommit
      };

      this.logger.debug('Git info retrieved', {
        rootPath,
        branch: gitInfo.branch,
        remoteCount: gitInfo.remotes.length,
        status: gitInfo.status
      });

      return gitInfo;

    } catch (error) {
      this.logger.debug('Failed to get git info', {
        rootPath,
        error: (error as Error).message
      });
      return undefined;
    }
  }

  private async getPackageInfo(rootPath: string): Promise<PackageInfo | undefined> {
    const packageFiles = [
      { file: 'package.json', parser: this.parsePackageJson },
      { file: 'requirements.txt', parser: this.parseRequirementsTxt },
      { file: 'Cargo.toml', parser: this.parseCargoToml },
      { file: 'go.mod', parser: this.parseGoMod },
      { file: 'pom.xml', parser: this.parsePomXml }
    ];

    for (const { file, parser } of packageFiles) {
      const filePath = path.join(rootPath, file);
      if (await fs.pathExists(filePath)) {
        try {
          return await parser.call(this, filePath);
        } catch (error) {
          this.logger.debug(`Failed to parse ${file}`, {
            filePath,
            error: (error as Error).message
          });
        }
      }
    }

    return undefined;
  }

  private async parsePackageJson(filePath: string): Promise<PackageInfo> {
    const content = await fs.readJson(filePath);
    return {
      name: content.name || 'unknown',
      version: content.version || '0.0.0',
      dependencies: content.dependencies || {},
      devDependencies: content.devDependencies || {},
      scripts: content.scripts || {}
    };
  }

  private async parseRequirementsTxt(filePath: string): Promise<PackageInfo> {
    const content = await fs.readFile(filePath, 'utf8');
    const dependencies: Record<string, string> = {};

    for (const line of content.split('\n')) {
      const trimmed = line.trim();
      if (trimmed && !trimmed.startsWith('#')) {
        const match = trimmed.match(/^([^=<>!]+)([=<>!].+)?$/);
        if (match) {
          dependencies[match[1].trim()] = match[2]?.trim() || '*';
        }
      }
    }

    return {
      name: path.basename(path.dirname(filePath)),
      version: '0.0.0',
      dependencies,
      devDependencies: {},
      scripts: {}
    };
  }

  private async parseCargoToml(filePath: string): Promise<PackageInfo> {
    try {
      const content = await fs.readFile(filePath, 'utf8');
      const parsed = toml.parse(content);

      const dependencies: Record<string, string> = {};
      const devDependencies: Record<string, string> = {};

      // Parse dependencies
      if (parsed.dependencies) {
        for (const [name, dep] of Object.entries(parsed.dependencies)) {
          if (typeof dep === 'string') {
            dependencies[name] = dep;
          } else if (typeof dep === 'object' && dep && 'version' in dep) {
            dependencies[name] = (dep as any).version;
          }
        }
      }

      // Parse dev dependencies
      if (parsed['dev-dependencies']) {
        for (const [name, dep] of Object.entries(parsed['dev-dependencies'])) {
          if (typeof dep === 'string') {
            devDependencies[name] = dep;
          } else if (typeof dep === 'object' && dep && 'version' in dep) {
            devDependencies[name] = (dep as any).version;
          }
        }
      }

      return {
        name: parsed.package?.name || 'rust-project',
        version: parsed.package?.version || '0.1.0',
        dependencies,
        devDependencies,
        scripts: {} // Rust doesn't have scripts like npm
      };
    } catch (error) {
      this.logger.debug('Failed to parse Cargo.toml', {
        filePath,
        error: (error as Error).message
      });

      return {
        name: 'rust-project',
        version: '0.1.0',
        dependencies: {},
        devDependencies: {},
        scripts: {}
      };
    }
  }

  private async parseGoMod(filePath: string): Promise<PackageInfo> {
    const content = await fs.readFile(filePath, 'utf8');
    const dependencies: Record<string, string> = {};
    
    const lines = content.split('\n');
    let inRequireBlock = false;
    let moduleName = 'go-project';

    for (const line of lines) {
      const trimmed = line.trim();
      
      if (trimmed.startsWith('module ')) {
        moduleName = trimmed.replace('module ', '');
      } else if (trimmed === 'require (') {
        inRequireBlock = true;
      } else if (trimmed === ')' && inRequireBlock) {
        inRequireBlock = false;
      } else if (inRequireBlock || trimmed.startsWith('require ')) {
        const match = trimmed.match(/^(?:require\s+)?([^\s]+)\s+([^\s]+)/);
        if (match) {
          dependencies[match[1]] = match[2];
        }
      }
    }

    return {
      name: moduleName,
      version: '0.0.0',
      dependencies,
      devDependencies: {},
      scripts: {}
    };
  }

  private async parsePomXml(filePath: string): Promise<PackageInfo> {
    // This would require an XML parser in a real implementation
    // For now, return basic info
    return {
      name: 'java-project',
      version: '1.0.0',
      dependencies: {},
      devDependencies: {},
      scripts: {}
    };
  }

  private extractDependencies(packageInfo?: PackageInfo): Record<string, string> {
    if (!packageInfo) {
      return {};
    }

    return {
      ...packageInfo.dependencies,
      ...packageInfo.devDependencies
    };
  }

  public async updateFileIndex(rootPath: string, filePath: string): Promise<FileInfo | null> {
    try {
      const absolutePath = path.resolve(rootPath, filePath);
      const stats = await fs.stat(absolutePath);

      const fileInfo: FileInfo = {
        path: filePath,
        type: stats.isDirectory() ? 'directory' : 'file',
        size: stats.size,
        lastModified: stats.mtime,
        permissions: stats.mode.toString(8)
      };

      // Read content for small text files
      const config = this.configManager.getConfig().context;
      if (stats.isFile() && 
          stats.size <= config.maxFileSize && 
          this.isTextFile(absolutePath)) {
        try {
          fileInfo.content = await fs.readFile(absolutePath, 'utf8');
        } catch (error) {
          // Skip files that can't be read as text
        }
      }

      return fileInfo;

    } catch (error) {
      this.logger.debug('Failed to update file index', {
        rootPath,
        filePath,
        error: (error as Error).message
      });
      return null;
    }
  }
}
